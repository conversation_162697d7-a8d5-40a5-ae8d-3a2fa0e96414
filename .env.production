# -----------------------------------------------------------------------------
# Web Information
# -----------------------------------------------------------------------------
NEXT_PUBLIC_WEB_URL = "kreaflux.org"
NEXT_PUBLIC_PROJECT_NAME = "KREA FLUX"
NEXT_PUBLIC_BRAND_NAME = "KREA FLUX"
NEXT_PUBLIC_BRAND_DOMAIN = "fluxkrea.top"
NEXT_PUBLIC_SUPPORT_EMAIL = "<EMAIL>"
NEXT_PUBLIC_SERVICE_DESCRIPTION = "KREA FLUX REALISTIC IMAGE GENERATOR"
NEXT_PUBLIC_BRAND_NAME ="KREA FLUX"

# -----------------------------------------------------------------------------
# Database with Supabase
# -----------------------------------------------------------------------------
# https://supabase.com/docs/guides/getting-started/quickstarts/nextjs
# Set your Supabase URL and Anon Key
SUPABASE_URL = https://momwtajfrafhozfgdbrg.supabase.co
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1vbXd0YWpmcmFmaG96ZmdkYnJnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5NzgxNzQsImV4cCI6MjA2OTU1NDE3NH0.UmcRX1ysgZlh0QDnNQdBsofXLbQJTv0mVFYcGvzqP_M"
# SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.dCNmWijUCwU3G1FD970NawFEgN0EFUVQyw4wbL7JCVU"


# -----------------------------------------------------------------------------
# Auth with next-auth
# https://authjs.dev/getting-started/installation?framework=Next.js
# Set your Auth URL and Secret
# Secret can be generated with `openssl rand -base64 32`
# -----------------------------------------------------------------------------
AUTH_SECRET = "Zt3BXVudzzRq2R2WBjames1dNMq48Gg9zKAYq7YwSL0="

# Google Auth
# https://authjs.dev/getting-started/providers/google
AUTH_GOOGLE_ID = "1011821969948-i0orej6mbfqon33v3klabnto7g49mvrh.apps.googleusercontent.com"
AUTH_GOOGLE_SECRET = "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"
NEXT_PUBLIC_AUTH_GOOGLE_ID = "1011821969948-i0orej6mbfqon33v3klabnto7g49mvrh.apps.googleusercontent.com"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "true"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "true"

# Github Auth
# https://authjs.dev/getting-started/providers/github
AUTH_GITHUB_ID = ""
AUTH_GITHUB_SECRET = ""
NEXT_PUBLIC_AUTH_GITHUB_ENABLED = "false"

# -----------------------------------------------------------------------------
# Analytics with Google Analytics
# https://analytics.google.com
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID = "G-DFL8TGPG03"

# -----------------------------------------------------------------------------
# Analytics with OpenPanel
# https://openpanel.dev
# -----------------------------------------------------------------------------
NEXT_PUBLIC_OPENPANEL_CLIENT_ID = ""

# Analytics with Plausible
# https://plausible.io/
NEXT_PUBLIC_PLAUSIBLE_DOMAIN = ""
NEXT_PUBLIC_PLAUSIBLE_SCRIPT_URL = ""



# Analytics with Clarity
# https://clarity.microsoft.com/projects
NEXT_PUBLIC_CLARITY_ID = "snteb2f8tj"




# -----------------------------------------------------------------------------
# Payment with Stripe
# https://docs.stripe.com/keys
# -----------------------------------------------------------------------------
STRIPE_PUBLIC_KEY = ""
STRIPE_PRIVATE_KEY = ""
STRIPE_WEBHOOK_SECRET = ""

NEXT_PUBLIC_PAY_SUCCESS_URL = "http://localhost:3000/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL = "http://localhost:3000/#pricing"
NEXT_PUBLIC_PAY_CANCEL_URL = "http://localhost:3000/#pricing"

NEXT_PUBLIC_LOCALE_DETECTION = "false"


ADMIN_EMAILS = <EMAIL>


NEXT_PUBLIC_DEFAULT_THEME = "light"
NEXT_PUBLIC_SINGLE_THEME = "ture"
# -----------------------------------------------------------------------------
# Storage with aws s3 sdk
# https://docs.aws.amazon.com/s3/index.html
# -----------------------------------------------------------------------------
STORAGE_ENDPOINT = "https://f4c8f86d0e8ec93da79ad6fa9d6eb165.r2.cloudflarestorage.com/fluxkrea"
STORAGE_REGION = "WNAM"
STORAGE_ACCESS_KEY = "bcc3acb8d9ee5e9025c40facc27ee242"
STORAGE_SECRET_KEY = "abc581dd072d3dcee5567ef92357bbfd65c2c3e0366aabcb00e3d1cf4f260e54"
STORAGE_BUCKET = "fluxkrea"
STORAGE_DOMAIN = "https://r2.kreaflux.org"

# Google Adsence Code
# https://adsense.com/
NEXT_PUBLIC_GOOGLE_ADCODE = ""


GRSAI_APIKEY = "sk-0e35869078a041b0848ddd907f65ce40"


REPLICATE_API_TOKEN = "****************************************"




# 火山引擎访问令牌
VOLCENGINE_ACCESS_TOKEN= "IBELn8vSLhaBRyVIS7XHI8_WJwY9YC64"

# 火山引擎应用ID
VOLCENGINE_APP_ID="1143142229"

# 回调URL基础地址（可选）
NEXT_PUBLIC_BASE_URL=https://your-domain.com

# 火山引擎服务配置（可选，使用默认值）
VOLCENGINE_BASE_URL=https://openspeech.bytedance.com
VOLCENGINE_TIMEOUT=60000
VOLCENGINE_RETRY_ATTEMPTS=3

# 回调验证密钥（可选，用于验证回调请求的合法性）
VOLCENGINE_CALLBACK_SECRET=your_callback_secret_here
